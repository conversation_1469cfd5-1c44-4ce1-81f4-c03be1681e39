/* Musik-Seite Spezifische Styles */

/* Breadcrumb Navigation */
.breadcrumb-container {
    background: #fff8ef;
    padding: 20px 0;
    border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
}

.breadcrumb-link {
    color: #4b879a;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #112736;
}

.breadcrumb-separator {
    margin: 0 10px;
    color: #999;
}

.breadcrumb-current {
    color: #112736;
    font-weight: 600;
}

/* Main Layout */
.musik-main {
    padding: 40px 0;
    min-height: calc(100vh - 200px);
}

.musik-header {
    text-align: center;
    margin-bottom: 40px;
}

.musik-title {
    font-size: 3rem;
    font-weight: 700;
    color: #112736;
    margin-bottom: 20px;
}

.musik-description {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Desktop Layout */
.desktop-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    margin-top: 40px;
}

/* Filter Controls */
.filter-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.filter-select {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #112736;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #4b879a;
}

.reset-button {
    padding: 8px 16px;
    background: #4b879a;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.reset-button:hover {
    background: #112736;
}

/* Song Table */
.song-table-wrapper {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 800px;
    overflow-y: auto;
    position: relative;
}

.song-table {
    width: 100%;
    border-collapse: collapse;
}

.song-table thead {
    background: #112736;
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

.song-table th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    cursor: pointer;
    user-select: none;
}

.song-table th.sortable:hover {
    background: #1a3a4a;
}

.sort-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sort-indicator.asc {
    border-bottom: 6px solid white;
    opacity: 1;
}

.sort-indicator.desc {
    border-top: 6px solid white;
    opacity: 1;
}

.header-action-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.header-action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.header-action-btn svg {
    width: 18px;
    height: 18px;
}

.ear-icon .strike-line {
    stroke: #ff4444;
    stroke-width: 3;
}

.song-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.song-table tbody tr:hover {
    background: #f8f9fa;
}

.song-table tbody tr.playing {
    background: #e8f4f8;
}

.song-table tbody tr.hidden {
    opacity: 0.4;
    background: #f5f5f5;
}

.song-table td {
    padding: 12px;
    font-size: 14px;
    color: #333;
}

.song-table td:first-child {
    font-weight: 600;
    color: #4b879a;
    width: 40px;
}

.song-title {
    font-weight: 600;
    color: #112736;
}

.song-album, .song-genre, .song-sprache {
    color: #666;
    font-size: 13px;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    color: #666;
}

.action-btn:hover {
    background: #f0f0f0;
    color: #112736;
}

.action-btn svg {
    width: 16px;
    height: 16px;
}

.hide-btn.active {
    color: #ff4444;
}

.hide-btn.active .strike-line {
    stroke: #ff4444;
}

/* Player Container */
.player-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.player-info {
    text-align: center;
    margin-bottom: 30px;
}

.album-cover-container {
    margin-bottom: 20px;
}

.album-cover {
    width: 280px;
    height: 280px;
    background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.album-cover.has-cover {
    background: none;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.album-cover:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.placeholder-icon {
    width: 80px;
    height: 80px;
    color: #999;
}

.current-song-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #112736;
    margin-bottom: 5px;
}

.current-artist {
    font-size: 1rem;
    color: #4b879a;
    margin-bottom: 5px;
}

.current-album {
    font-size: 0.9rem;
    color: #666;
}

.album-link {
    color: #4b879a;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.3s ease;
}

.album-link:hover {
    color: #112736;
    text-decoration: underline;
}

/* Player Controls */
.player-controls {
    margin-bottom: 30px;
}

.control-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.control-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #112736;
}

.control-btn:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.play-pause-btn {
    width: 60px;
    height: 60px;
    background: #4b879a;
    color: white;
}

.play-pause-btn:hover {
    background: #112736;
}

.control-btn svg {
    width: 24px;
    height: 24px;
}

.play-pause-btn svg {
    width: 28px;
    height: 28px;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.time-display {
    font-size: 12px;
    color: #666;
    min-width: 35px;
}

.progress-bar-container {
    flex: 1;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    position: relative;
    cursor: pointer;
}

.progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: #4b879a;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(50%, -50%);
    width: 14px;
    height: 14px;
    background: #4b879a;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.progress-bar-container:hover .progress-handle {
    opacity: 1;
}

.additional-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.additional-controls .control-btn {
    width: 40px;
    height: 40px;
}

.additional-controls .control-btn svg {
    width: 20px;
    height: 20px;
}

.control-btn.active {
    background: #4b879a;
    color: white;
}

.control-btn.single-repeat {
    position: relative;
}

.control-btn.single-repeat .repeat-one {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Lyrics */
.lyrics-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.lyrics-header {
    margin-bottom: 15px;
}

.lyrics-tabs {
    display: flex;
    gap: 2px;
    background: transparent;
    border-bottom: 1px solid #e9ecef;
}

.lyrics-tab {
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 500;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    white-space: nowrap;
    position: relative;
}

.lyrics-tab:hover {
    color: #4b879a;
    border-bottom-color: #dee2e6;
}

.lyrics-tab.active {
    color: #4b879a;
    border-bottom-color: #4b879a;
    font-weight: 600;
}

.lyrics-tab[style*="display: none"] {
    display: none !important;
}

.lyrics-content {
    font-size: 16px;
    line-height: 1.6;
    color: #555;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 10px;
    box-sizing: border-box;
}

.lyrics-content p {
    margin-bottom: 15px;
    margin-top: 0;
}

.lyrics-content p:last-child {
    margin-bottom: 0;
}

.no-lyrics {
    color: #999;
    font-style: italic;
}

/* Responsive Lyrics Tabs */
@media (max-width: 768px) {
    .lyrics-tabs {
        justify-content: flex-start;
    }

    .lyrics-tab {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Mobile Layout */
.mobile-layout {
    display: none;
    overflow: visible;
}

/* Mobile Filter Toggle Button */
.mobile-filter-toggle-container {
    margin-bottom: 15px;
    padding: 0 5px;
}

.mobile-filter-toggle-btn {
    width: 100%;
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    color: #112736;
    font-weight: 500;
}

.mobile-filter-toggle-btn:hover {
    background: #e9ecef;
    border-color: #4b879a;
}

.mobile-filter-toggle-btn.active {
    background: #e8f4f8;
    border-color: #4b879a;
    color: #4b879a;
}

.filter-toggle-icon {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.mobile-filter-toggle-btn.active .filter-toggle-icon {
    transform: rotate(180deg);
}

/* Mobile Filter Controls */
.mobile-filter-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
    padding: 0 5px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.mobile-filter-controls.show {
    display: flex;
}

.mobile-filter-controls .filter-select {
    width: 100%;
    font-size: 16px; /* Verhindert Zoom auf iOS */
}

.mobile-control-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    padding: 0 5px;
}

.mobile-control-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #112736;
}

.mobile-control-btn:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.mobile-control-btn.active {
    background: #4b879a;
    color: white;
}

.mobile-control-btn.single-repeat {
    position: relative;
}

.mobile-control-btn.single-repeat .repeat-one {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Desktop Song Menu */
.desktop-song-menu {
    position: relative;
    display: inline-block;
}

.desktop-menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    color: #666;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.desktop-menu-btn:hover {
    background: #f0f0f0;
    color: #112736;
}

.desktop-menu-btn svg {
    width: 16px;
    height: 16px;
}

.desktop-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 200px;
    overflow: hidden;
}

.desktop-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: #333;
}

.desktop-menu-item:hover {
    background: #f8f9fa;
}

.desktop-menu-item:first-child {
    border-radius: 8px 8px 0 0;
}

.desktop-menu-item:last-child {
    border-radius: 0 0 8px 8px;
}

.desktop-menu-item svg {
    width: 16px;
    height: 16px;
    color: #666;
    flex-shrink: 0;
}

.desktop-menu-item span {
    flex: 1;
}

/* Actions Column */
.actions-column {
    width: 60px;
    text-align: center;
}

.actions-cell {
    text-align: center;
}

/* Filter Dropdowns */
.filter-controls {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 200px;
}

.filter-label {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.filter-dropdown {
    position: relative;
}

.filter-dropdown-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 12px 16px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: all 0.3s ease;
}

.filter-dropdown-btn:hover {
    border-color: #4b879a;
    background: #f8f9fa;
}

.filter-dropdown-btn:focus {
    outline: none;
    border-color: #4b879a;
    box-shadow: 0 0 0 3px rgba(75, 135, 154, 0.1);
}

.filter-text {
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.filter-arrow {
    width: 16px;
    height: 16px;
    color: #666;
    transition: transform 0.3s ease;
    flex-shrink: 0;
    margin-left: 8px;
}

.filter-dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}



.filter-options {
    padding: 8px 0;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.filter-option:hover {
    background: #f8f9fa;
}

.filter-option input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.filter-option label {
    flex: 1;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    margin: 0;
}

/* "Alle [Kategorie]" Option Styling */
.filter-all-option {
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 8px;
    padding-bottom: 8px;
}

.filter-all-label {
    font-weight: 600;
    color: #4b879a !important;
}

.filter-separator {
    height: 1px;
    background: #e0e0e0;
    margin: 8px 0;
}

/* Mobile Filter Anpassungen */
.mobile-filter-controls {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    margin-bottom: 20px;
    position: relative;
    overflow: visible;
}

.mobile-filter-controls .filter-controls {
    flex-direction: column;
    gap: 16px;
}

.mobile-filter-controls .filter-group {
    min-width: auto;
    position: relative;
    overflow: visible;
}

.mobile-filter-controls .filter-dropdown {
    position: relative;
    overflow: visible;
}

.mobile-filter-controls .filter-dropdown-btn {
    padding: 14px 16px;
    font-size: 16px;
}

/* Mobile Dropdown-Spezifische Anpassungen */
.mobile-filter-controls .filter-dropdown-content {
    max-height: 250px;
    z-index: 9999;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
}

/* Filter Anwenden Button */
.filter-apply-group {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
}

.filter-apply-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.filter-apply-btn:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.filter-apply-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.filter-apply-btn svg {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
}

.filter-apply-btn span {
    white-space: nowrap;
}

/* Mobile Filter Anwenden Button */
.mobile-filter-apply-btn {
    width: 100%;
    justify-content: center;
    margin-top: 16px;
    padding: 14px 20px;
    font-size: 16px;
}

.mobile-filter-apply-btn svg {
    width: 20px;
    height: 20px;
}

/* Responsive Anpassungen */
@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
        gap: 16px;
    }

    .filter-group {
        min-width: auto;
    }

    .filter-apply-group {
        margin-left: 0;
        margin-top: 8px;
    }
}

.mobile-control-btn svg {
    width: 20px;
    height: 20px;
}

/* Mobile Song List */
.mobile-song-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.mobile-song-item:hover {
    transform: translateY(-2px);
}

.mobile-song-item.playing {
    background: #e8f4f8;
    border-left: 4px solid #4b879a;
}

.mobile-song-content {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.mobile-album-cover {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
}

.mobile-album-cover.has-cover {
    background: #f0f0f0;
}

.mobile-album-cover .placeholder-icon {
    width: 24px;
    height: 24px;
    color: #999;
}

.mobile-song-info {
    flex: 1;
    min-width: 0;
}

.mobile-song-title {
    font-weight: 600;
    color: #112736;
    font-size: 16px;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mobile-song-album {
    color: #666;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Mobile Song Menu */
.mobile-song-menu {
    position: relative;
    margin-left: auto;
}

.mobile-menu-btn {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
    color: #666;
}

.mobile-menu-btn:hover {
    background: #f0f0f0;
}

.mobile-menu-btn svg {
    width: 20px;
    height: 20px;
}

.mobile-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e0e0e0;
    min-width: 180px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    backdrop-filter: none;
}

.mobile-menu-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mobile-menu-item {
    width: 100%;
    background: none;
    border: none;
    padding: 12px 16px;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: #112736;
    transition: background-color 0.3s ease;
}

.mobile-menu-item:hover {
    background: #f8f9fa;
}

.mobile-menu-item:first-child {
    border-radius: 8px 8px 0 0;
}

.mobile-menu-item:last-child {
    border-radius: 0 0 8px 8px;
}

.mobile-menu-item svg {
    width: 16px;
    height: 16px;
    color: #666;
    flex-shrink: 0;
}

.mobile-menu-item span {
    flex: 1;
}

/* Mini Player */
.mini-player {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e0e0e0;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.mini-player-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    cursor: pointer;
    padding: 5px;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.mini-player-info:hover {
    background: rgba(75, 135, 154, 0.1);
}

.mini-album-cover {
    width: 40px;
    height: 40px;
    background: #f0f0f0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: all 0.3s ease;
}

.mini-album-cover.has-cover {
    background: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mini-album-cover svg {
    width: 20px;
    height: 20px;
    color: #999;
}

.mini-song-info {
    flex: 1;
}

.mini-song-title {
    font-weight: 600;
    color: #112736;
    font-size: 14px;
    display: block;
}

.mini-artist {
    color: #666;
    font-size: 12px;
}

.mini-controls {
    display: flex;
    gap: 10px;
}

.mini-control-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    color: #112736;
}

.mini-control-btn:hover {
    background: #f0f0f0;
}

.mini-control-btn svg {
    width: 20px;
    height: 20px;
}

/* Modal Player - New Design */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.modal-player {
    background: white;
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Close Button - Overlapping */
.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.6);
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.modal-close svg {
    width: 24px;
    height: 24px;
    color: white;
}

/* Upper Section - Album Cover Area */
.modal-upper-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    min-height: 50vh;
}

/* Tab Navigation */
.modal-tab-navigation {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 4px;
    gap: 4px;
    z-index: 5;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.modal-tab {
    background: transparent;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
}

.modal-tab:hover {
    color: #4b879a;
}

.modal-tab.active {
    background: #4b879a;
    color: white;
    font-weight: 600;
}

.modal-tab[style*="display: none"] {
    display: none !important;
}

/* Tab Content Area */
.modal-tab-content-area {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.modal-tab-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px 20px 80px;
}

.modal-tab-content.active {
    opacity: 1;
    transform: translateX(0);
}

.modal-tab-content.slide-left {
    transform: translateX(-100%);
}

/* Album Cover Tab */
.modal-tab-content[data-content="cover"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.modal-album-cover {
    width: 280px;
    height: 280px;
    border-radius: 20px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    position: relative;
}

.modal-album-cover.has-cover {
    background: none;
}

.modal-album-cover .placeholder-icon {
    width: 80px;
    height: 80px;
    color: #ccc;
}

.modal-song-info {
    text-align: center;
    color: white;
}

.modal-song-title {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.modal-artist {
    font-size: 16px;
    margin: 0 0 4px 0;
    opacity: 0.9;
}

.modal-album {
    font-size: 14px;
    margin: 0;
    opacity: 0.8;
}

/* Lyrics and Translation Tabs */
.modal-tab-content[data-content="lyrics"],
.modal-tab-content[data-content="translation"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    justify-content: flex-start;
    padding-top: 60px;
}

.modal-lyrics-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    max-height: 70vh;
    overflow-y: auto;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 350px;
}

.modal-lyrics-content p {
    margin-bottom: 15px;
    margin-top: 0;
}

.modal-lyrics-content p:last-child {
    margin-bottom: 0;
}

.no-lyrics {
    color: #666;
    font-style: italic;
    text-align: center;
}

/* Info Tab */
.modal-tab-content[data-content="info"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    justify-content: flex-start;
    padding-top: 60px;
}

.modal-info-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    width: 100%;
    max-width: 350px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.info-value {
    color: #333;
    font-size: 14px;
    text-align: right;
    flex: 1;
    margin-left: 20px;
}

.info-additional {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.info-description {
    font-size: 14px;
    line-height: 1.5;
    color: #666;
    margin: 0;
}

/* Lower Section - Player Controls */
.modal-lower-section {
    background: white;
    padding: 20px;
    border-top: 1px solid #eee;
    flex-shrink: 0;
}

.modal-player-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.modal-control-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
}

.modal-control-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #4b879a;
}

.modal-control-btn:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.modal-control-btn.active {
    background: #4b879a;
    color: white;
}

.modal-play-pause-btn {
    width: 60px;
    height: 60px;
    background: #4b879a;
    color: white;
}

.modal-play-pause-btn:hover {
    background: #3a6b7a;
    transform: scale(1.05);
}

.modal-control-btn svg {
    width: 24px;
    height: 24px;
}

.modal-play-pause-btn svg {
    width: 28px;
    height: 28px;
}

/* Progress Bar */
.modal-progress-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.modal-time-display {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    min-width: 35px;
    text-align: center;
}

.modal-progress-bar-container {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    cursor: pointer;
    position: relative;
}

.modal-progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}

.modal-progress-fill {
    height: 100%;
    background: #4b879a;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.modal-progress-handle {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(50%, -50%);
    width: 16px;
    height: 16px;
    background: #4b879a;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.modal-progress-bar-container:hover .modal-progress-handle {
    opacity: 1;
}

/* Additional Controls */
.modal-additional-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Desktop Responsive Design */
@media (min-width: 769px) {
    .modal-overlay {
        padding: 20px;
    }

    .modal-player {
        width: 90vw;
        max-width: 500px;
        height: 80vh;
        max-height: 700px;
        border-radius: 20px;
        overflow: hidden;
    }

    .modal-close {
        top: 15px;
        right: 15px;
    }

    .modal-album-cover {
        width: 320px;
        height: 320px;
    }

    .modal-song-title {
        font-size: 28px;
    }

    .modal-artist {
        font-size: 18px;
    }

    .modal-album {
        font-size: 16px;
    }

    .modal-lyrics-content,
    .modal-info-content {
        max-width: 400px;
        padding: 40px;
    }

    .modal-control-btn {
        width: 55px;
        height: 55px;
    }

    .modal-play-pause-btn {
        width: 70px;
        height: 70px;
    }

    .modal-control-btn svg {
        width: 26px;
        height: 26px;
    }

    .modal-play-pause-btn svg {
        width: 32px;
        height: 32px;
    }
}

/* Touch and Swipe Support */
.modal-tab-content-area {
    touch-action: pan-x;
}

.modal-tab-content {
    user-select: none;
}

/* Smooth scrolling for lyrics */
.modal-lyrics-content {
    scroll-behavior: smooth;
}

.modal-lyrics-content::-webkit-scrollbar {
    width: 6px;
}

.modal-lyrics-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.modal-lyrics-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.modal-lyrics-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .desktop-layout {
        grid-template-columns: 1fr 350px;
        gap: 30px;
    }
    
    .player-container {
        padding: 20px;
    }
    
    .album-cover {
        width: 220px;
        height: 220px;
    }
}

@media (max-width: 768px) {
    .desktop-layout {
        display: none;
    }
    
    .mobile-layout {
        display: block;
        margin-top: 20px;
        overflow: visible;
    }
    
    .musik-title {
        font-size: 2rem;
    }
    
    .musik-description {
        font-size: 1rem;
        padding: 0 20px;
    }
    
    .container {
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .musik-main {
        padding: 20px 0;
    }

    .musik-title {
        font-size: 1.8rem;
    }

    .breadcrumb-container {
        padding: 15px 0;
    }

    .container {
        padding: 0 15px;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Loading States */
.song-table.loading tbody {
    opacity: 0.5;
}

.player-container.loading {
    opacity: 0.7;
}

/* Focus States for Accessibility */
.control-btn:focus,
.action-btn:focus,
.header-action-btn:focus {
    outline: 2px solid #4b879a;
    outline-offset: 2px;
}

.song-table tbody tr:focus {
    outline: 2px solid #4b879a;
    outline-offset: -2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .song-table th {
        border: 1px solid #000;
    }

    .song-table td {
        border: 1px solid #666;
    }

    .control-btn {
        border: 2px solid #000;
    }
}
