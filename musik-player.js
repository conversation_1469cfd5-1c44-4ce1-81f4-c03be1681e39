/**
 * Musik Player JavaScript
 * Hauptfunktionalität für die Musikseite
 */

class MusikPlayer {
    constructor() {
        this.songs = [];
        this.currentSongIndex = -1;
        this.isPlaying = false;
        this.isShuffleMode = false;
        this.repeatMode = 'none'; // 'none', 'playlist', 'single'
        this.currentSort = { column: 'nummer', direction: 'asc' };
        this.hiddenSongs = new Set();
        this.filteredSongs = [];

        // Shuffle-Verwaltung
        this.shuffleHistory = [];
        this.shuffleQueue = [];

        // Warteschlange (Queue)
        this.playQueue = [];

        // Filter-Zustand
        this.selectedFilters = {
            genre: new Set(),
            sprache: new Set(),
            album: new Set()
        };

        // Media Session
        this.mediaSessionHandlersSet = false;
        this.currentCoverPath = null;
        
        // Audio Element
        this.audio = document.getElementById('audio-player');

        // Debouncing für Song-Auswahl
        this.songSelectionTimeout = null;
        this.isChangingSong = false;

        // DOM Elements
        this.songTableBody = document.getElementById('song-table-body');
        this.mobileSongList = document.getElementById('mobile-song-list');


        this.currentSongTitle = document.getElementById('current-song-title');
        this.currentArtist = document.getElementById('current-artist');
        this.currentAlbum = document.getElementById('current-album');
        this.playPauseBtn = document.getElementById('play-pause-btn');
        this.progressFill = document.getElementById('progress-fill');
        this.progressHandle = document.getElementById('progress-handle');
        this.currentTimeDisplay = document.getElementById('current-time');
        this.totalTimeDisplay = document.getElementById('total-time');
        this.lyricsContent = document.getElementById('lyrics-content');
        this.lyricsTranslation = document.getElementById('lyrics-translation');
        this.lyricsInfo = document.getElementById('lyrics-info');
        this.lyricsTabs = document.getElementById('lyrics-tabs');

        // Mobile Elements
        this.miniPlayer = document.getElementById('mini-player');
        this.miniSongTitle = document.getElementById('mini-song-title');
        this.miniPlayPause = document.getElementById('mini-play-pause');
        this.modalOverlay = document.getElementById('modal-overlay');
        this.modalPlayer = document.getElementById('modal-player');
    }

    initialize() {
        this.init();
    }
    
    init() {
        this.loadSongs();
        this.setupEventListeners();
        this.setupFilters();
        this.renderSongTable();
        this.renderMobileSongList();
        this.updateRepeatButtons(); // Initial button state setzen
    }
    
    loadSongs() {
        if (typeof MUSIK_DATEN === 'undefined') {
            this.songs = [];
            this.filteredSongs = [];
            return;
        }

        // Songs aus musik-data.js laden und mit Index versehen
        this.songs = MUSIK_DATEN.map((song, index) => ({
            ...song,
            id: index + 1,
            nummer: index + 1,
            isHidden: false
        }));

        // Gefilterte Songs initial auf alle Songs setzen
        this.filteredSongs = [...this.songs];
    }
    
    setupEventListeners() {
        // Audio Events
        this.audio.addEventListener('loadedmetadata', () => {
            this.updateDuration();
            this.updateMediaSessionPosition();
        });
        this.audio.addEventListener('timeupdate', () => {
            this.updateProgress();
            this.updateModalProgressIfOpen();
            this.updateMediaSessionPosition();
        });
        this.audio.addEventListener('ended', () => this.nextSong());
        this.audio.addEventListener('error', (e) => this.handleAudioError(e));
        this.audio.addEventListener('play', () => {
            this.isPlaying = true;
            this.updatePlayButton();
            this.updateMediaSessionPlaybackState();
        });
        this.audio.addEventListener('pause', () => {
            this.isPlaying = false;
            this.updatePlayButton();
            this.updateMediaSessionPlaybackState();
        });
        
        // Player Controls
        this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        document.getElementById('prev-btn').addEventListener('click', () => this.previousSong());
        document.getElementById('next-btn').addEventListener('click', () => this.nextSong());
        document.getElementById('shuffle-btn').addEventListener('click', () => this.toggleShuffle());
        document.getElementById('repeat-btn').addEventListener('click', () => this.toggleRepeat());
        
        // Progress Bar
        const progressContainer = document.querySelector('.progress-bar-container');
        progressContainer.addEventListener('click', (e) => this.seekTo(e));
        
        // Table Sorting
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => this.sortTable(header.dataset.column));
        });
        
        // Filter Controls werden jetzt in setupSimpleFilters() behandelt

        // Lyrics Tabs
        this.setupLyricsTabs();

        // Album Filter Click
        document.getElementById('current-album').addEventListener('click', () => this.filterByCurrentAlbum());

        // Mobile Filter Toggle
        document.getElementById('mobile-filter-toggle').addEventListener('click', () => this.toggleMobileFilters());



        // Mobile Control Buttons
        document.getElementById('mobile-shuffle-btn').addEventListener('click', () => this.toggleShuffle());
        document.getElementById('mobile-repeat-btn').addEventListener('click', () => this.toggleRepeat());
        document.getElementById('mobile-reset-filters').addEventListener('click', () => this.resetFilters());
        
        // Header Actions
        document.getElementById('clear-filters').addEventListener('click', () => this.clearFiltersAndQueue());
        document.getElementById('download-current-btn').addEventListener('click', () => this.downloadCurrent());

        // Close menus when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.mobile-song-menu')) {
                this.closeMobileMenus();
            }
            if (!e.target.closest('.desktop-song-menu')) {
                this.closeDesktopMenus();
            }
        });
        
        // Mobile Events
        if (this.miniPlayPause) {
            this.miniPlayPause.addEventListener('click', () => {
                this.togglePlayPause();
            });
        }

        const miniExpandBtn = document.getElementById('mini-expand');
        if (miniExpandBtn) {
            miniExpandBtn.addEventListener('click', () => {
                this.openMobilePlayer();
            });
        }

        const miniPlayerInfo = document.getElementById('mini-player-info');
        if (miniPlayerInfo) {
            miniPlayerInfo.addEventListener('click', () => {
                this.openMobilePlayer();
            });
        }

        const modalCloseBtn = document.getElementById('modal-close');
        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', () => this.closeMobilePlayer());
        }

        if (this.modalOverlay) {
            this.modalOverlay.addEventListener('click', (e) => {
                if (e.target === this.modalOverlay) this.closeMobilePlayer();
            });
        }
        
        // Keyboard Shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // Window Resize für Mini-Player
        window.addEventListener('resize', () => this.handleResize());
    }
    
    renderSongTable() {
        if (!this.songTableBody) {
            return;
        }

        this.songTableBody.innerHTML = '';

        this.filteredSongs.forEach((song) => {
            const row = document.createElement('tr');
            row.dataset.songId = song.id;
            row.className = song.isHidden ? 'hidden' : '';
            
            row.innerHTML = `
                <td>${song.nummer}</td>
                <td class="song-title">${song.titel}</td>
                <td class="song-album">${song.album}</td>
                <td class="song-genre">${song.genre}</td>
                <td class="song-sprache">${song.sprache}</td>
                <td class="actions-cell">
                    <div class="desktop-song-menu">
                        <button class="desktop-menu-btn" data-song-id="${song.id}" title="Aktionen">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <circle cx="12" cy="12" r="1"/>
                                <circle cx="12" cy="5" r="1"/>
                                <circle cx="12" cy="19" r="1"/>
                            </svg>
                        </button>
                        <div class="desktop-menu-dropdown" data-song-id="${song.id}" style="display: none;">
                            <div class="desktop-menu-item" data-action="queue" data-song-id="${song.id}">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                                <span>Zur Warteschlange hinzufügen</span>
                            </div>
                            <div class="desktop-menu-item" data-action="hide" data-song-id="${song.id}">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 2C8 2 5 5 5 9v6c0 1 1 2 2 2h1v-8c0-2.5 2.5-5 5-5s5 2.5 5 5v8h1c1 0 2-1 2-2V9c0-4-3-7-7-7z"/>
                                    ${song.isHidden ? '<line x1="2" y1="2" x2="22" y2="22" class="strike-line"/>' : ''}
                                </svg>
                                <span>${song.isHidden ? 'Einblenden' : 'Ausblenden'}</span>
                            </div>
                            <div class="desktop-menu-item" data-action="download" data-song-id="${song.id}">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                <span>Herunterladen</span>
                            </div>
                        </div>
                    </div>
                </td>
            `;
            
            // Event Listeners für die Zeile
            row.addEventListener('click', (e) => {
                if (!e.target.closest('.desktop-song-menu')) {
                    this.playSong(song.id - 1);
                }
            });

            // Event Listeners für Desktop-Menü
            const menuBtn = row.querySelector('.desktop-menu-btn');
            menuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDesktopMenu(song.id);
            });

            // Event Listeners für Desktop-Menü Items
            const menuItems = row.querySelectorAll('.desktop-menu-item');
            menuItems.forEach(menuItem => {
                menuItem.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = menuItem.dataset.action;
                    const songId = parseInt(menuItem.dataset.songId);
                    this.handleDesktopMenuAction(action, songId);
                    this.closeDesktopMenus();
                });
            });
            
            this.songTableBody.appendChild(row);
        });
    }
    
    renderMobileSongList() {
        this.mobileSongList.innerHTML = '';

        this.filteredSongs.forEach((song) => {
            if (song.isHidden) return;

            const item = document.createElement('div');
            item.className = 'mobile-song-item';
            item.dataset.songId = song.id;

            item.innerHTML = `
                <div class="mobile-song-content">
                    <div class="mobile-album-cover" data-song-number="${song.nummer}">
                        <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                        </svg>
                    </div>
                    <div class="mobile-song-info">
                        <div class="mobile-song-title">${song.titel}</div>
                        <div class="mobile-song-album">${song.album}</div>
                    </div>
                    <div class="mobile-song-menu">
                        <button class="mobile-menu-btn" data-song-id="${song.id}" title="Aktionen">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <circle cx="12" cy="12" r="1"/>
                                <circle cx="12" cy="5" r="1"/>
                                <circle cx="12" cy="19" r="1"/>
                            </svg>
                        </button>
                        <div class="mobile-menu-dropdown" data-song-id="${song.id}">
                            <button class="mobile-menu-item" data-action="queue" data-song-id="${song.id}">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                                <span>Zur Warteschlange hinzufügen</span>
                            </button>
                            <button class="mobile-menu-item" data-action="hide" data-song-id="${song.id}">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 2C8 2 5 5 5 9v6c0 1 1 2 2 2h1v-8c0-2.5 2.5-5 5-5s5 2.5 5 5v8h1c1 0 2-1 2-2V9c0-4-3-7-7-7z"/>
                                    <line x1="2" y1="2" x2="22" y2="22" class="strike-line"/>
                                </svg>
                                <span>Lied ausblenden</span>
                            </button>
                            <button class="mobile-menu-item" data-action="download" data-song-id="${song.id}">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                <span>Lied downloaden</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Song-Item Click (nur wenn nicht auf Menü geklickt)
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.mobile-song-menu')) {
                    this.safeMobileSongSelection(song.id - 1);
                }
            });

            // Menü Button Event Listener
            const menuBtn = item.querySelector('.mobile-menu-btn');
            menuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleMobileMenu(song.id);
            });

            // Menü Item Event Listeners
            const menuItems = item.querySelectorAll('.mobile-menu-item');
            menuItems.forEach(menuItem => {
                menuItem.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = menuItem.dataset.action;
                    const songId = parseInt(menuItem.dataset.songId);
                    this.handleMobileMenuAction(action, songId);
                    this.closeMobileMenus();
                });
            });

            // Album Cover laden
            this.loadMobileAlbumCover(item.querySelector('.mobile-album-cover'), song.nummer);

            this.mobileSongList.appendChild(item);
        });
    }
    
    setupFilters() {
        // Eindeutige Werte für alle Filter sammeln
        const genres = [...new Set(this.songs.map(song => song.genre))].sort();
        const sprachen = [...new Set(this.songs.map(song => song.sprache))].sort();
        const albums = [...new Set(this.songs.map(song => song.album))].sort();

        // Alle Filter initial als ausgewählt setzen (zeigt alle Lieder)
        this.selectedFilters.genre = new Set(genres);
        this.selectedFilters.sprache = new Set(sprachen);
        this.selectedFilters.album = new Set(albums);

        // Multiple Choice Filter erstellen
        this.createFilterOptions('genre', genres, false);
        this.createFilterOptions('sprache', sprachen, false);
        this.createFilterOptions('album', albums, false);

        // Mobile Filter erstellen
        this.createFilterOptions('genre', genres, true);
        this.createFilterOptions('sprache', sprachen, true);
        this.createFilterOptions('album', albums, true);

        // Event Listeners für Filter-Dropdowns mit Delay
        setTimeout(() => {
            this.setupFilterEventListeners();
        }, 100);

        // Initial alle Checkboxen aktivieren und Button-Texte setzen
        setTimeout(() => {
            this.updateAllFilterCheckboxes();
            // Zusätzliche Synchronisation für "Alle [Kategorie]" Checkboxen
            setTimeout(() => {
                this.syncAllCategoryCheckboxes();
            }, 100);
        }, 200);

        // Backup Event Listener direkt setzen
        setTimeout(() => {
            const btn = document.getElementById('apply-filters-btn');
            const mobileBtn = document.getElementById('mobile-apply-filters-btn');

            if (btn) {
                btn.onclick = () => {
                    this.applyFilters();
                };
            }

            if (mobileBtn) {
                mobileBtn.onclick = () => {
                    this.applyFilters();
                };
            }
        }, 500);


    }

    createFilterOptions(filterType, options, isMobile) {
        const prefix = isMobile ? 'mobile-' : '';
        const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);

        if (!optionsContainer) {
            return;
        }

        optionsContainer.innerHTML = '';

        // "Alle [Kategorie]" Toggle-Button hinzufügen
        const allOptionDiv = document.createElement('div');
        allOptionDiv.className = 'filter-option filter-all-option';
        allOptionDiv.addEventListener('click', (e) => e.stopPropagation());

        const allCheckbox = document.createElement('input');
        allCheckbox.type = 'checkbox';
        allCheckbox.id = `${prefix}${filterType}-alle`;
        allCheckbox.value = 'alle';
        allCheckbox.checked = true; // Initial alle ausgewählt (wird später synchronisiert)
        allCheckbox.addEventListener('change', (e) => {
            e.stopPropagation();
            this.handleAllFilterToggle(filterType, e.target.checked, isMobile);
        });

        const allLabel = document.createElement('label');
        allLabel.htmlFor = allCheckbox.id;
        allLabel.textContent = `Alle ${this.getFilterDisplayName(filterType)}`;
        allLabel.className = 'filter-all-label';
        allLabel.addEventListener('click', (e) => e.stopPropagation());

        allOptionDiv.appendChild(allCheckbox);
        allOptionDiv.appendChild(allLabel);
        optionsContainer.appendChild(allOptionDiv);

        // Trennlinie hinzufügen
        const separator = document.createElement('div');
        separator.className = 'filter-separator';
        optionsContainer.appendChild(separator);

        // Einzelne Optionen hinzufügen
        options.forEach(option => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'filter-option';
            optionDiv.addEventListener('click', (e) => e.stopPropagation());

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `${prefix}${filterType}-${option.toLowerCase().replace(/\s+/g, '-')}`;
            checkbox.value = option;
            checkbox.checked = true; // Initial alle ausgewählt
            checkbox.addEventListener('change', (e) => {
                e.stopPropagation();
                this.handleFilterChange(filterType, option, e.target.checked);
            });

            const label = document.createElement('label');
            label.htmlFor = checkbox.id;
            label.textContent = option;
            label.addEventListener('click', (e) => e.stopPropagation());

            optionDiv.appendChild(checkbox);
            optionDiv.appendChild(label);
            optionsContainer.appendChild(optionDiv);
        });
    }

    setupFilterEventListeners() {
        // Dropdown-Buttons
        const filterTypes = ['genre', 'sprache', 'album'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const btn = document.getElementById(`${prefix}${filterType}-filter-btn`);
                const dropdown = document.getElementById(`${prefix}${filterType}-filter-dropdown`);

                if (btn && dropdown) {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleFilterDropdown(dropdown);
                    });
                } else {
                    console.warn(`Filter elements not found: ${prefix}${filterType}-filter-btn or ${prefix}${filterType}-filter-dropdown`);
                }
            });
        });



        // Filter Anwenden Buttons
        const applyFiltersBtn = document.getElementById('apply-filters-btn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.closeAllFilterDropdowns(false);
                this.applyFilters();
            });
        }

        const mobileApplyFiltersBtn = document.getElementById('mobile-apply-filters-btn');
        if (mobileApplyFiltersBtn) {
            mobileApplyFiltersBtn.addEventListener('click', () => {
                this.closeAllFilterDropdowns(false);
                this.applyFilters();
            });
        }

        // Reset-Buttons Event Listener
        const clearFiltersBtn = document.getElementById('clear-filters');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => this.clearFiltersAndQueue());
        }

        const mobileResetBtn = document.getElementById('mobile-reset-filters');
        if (mobileResetBtn) {
            mobileResetBtn.addEventListener('click', () => this.clearFiltersAndQueue());
        }

        // Dropdown schließen wenn außerhalb geklickt wird (OHNE Filter anzuwenden)
        document.addEventListener('click', () => {
            this.closeAllFilterDropdowns(false);
        });
    }

    handleFilterChange(filterType, option, isChecked) {
        if (isChecked) {
            this.selectedFilters[filterType].add(option);
        } else {
            this.selectedFilters[filterType].delete(option);
        }

        // "Alle [Kategorie]" Checkbox aktualisieren
        this.updateAllFilterCheckbox(filterType);

        this.updateFilterButtonText(filterType);

        // Filter werden nur noch über den "Filter anwenden" Button angewendet
    }

    handleAllFilterToggle(filterType, isChecked, isMobile) {
        const prefix = isMobile ? 'mobile-' : '';
        const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);

        if (!optionsContainer) return;

        // Alle einzelnen Checkboxen (außer der "Alle" Checkbox) finden
        const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');

        if (isChecked) {
            // Alle auswählen
            individualCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                this.selectedFilters[filterType].add(checkbox.value);
            });
        } else {
            // Alle abwählen
            individualCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                this.selectedFilters[filterType].delete(checkbox.value);
            });
        }

        // Auch die andere Plattform (Desktop/Mobile) synchronisieren
        this.syncAllFilterCheckboxes(filterType, isChecked);

        this.updateFilterButtonText(filterType);

        // Filter werden nur noch über den "Filter anwenden" Button angewendet
    }

    updateAllFilterCheckbox(filterType) {
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);

            if (!optionsContainer) return;

            const allCheckbox = optionsContainer.querySelector('input[value="alle"]');
            const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');

            if (allCheckbox && individualCheckboxes.length > 0) {
                const allChecked = Array.from(individualCheckboxes).every(cb => cb.checked);
                allCheckbox.checked = allChecked;
            }
        });
    }

    syncAllFilterCheckboxes(filterType, isChecked) {
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);
            if (!optionsContainer) return;

            const allCheckbox = optionsContainer.querySelector('input[value="alle"]');
            const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');

            if (allCheckbox) {
                allCheckbox.checked = isChecked;
            }

            individualCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
    }



    updateFilterButtonText(filterType) {
        const selectedCount = this.selectedFilters[filterType].size;
        const totalOptionsElement = document.querySelector(`#${filterType}-filter-options`);
        const totalOptions = totalOptionsElement ?
            totalOptionsElement.querySelectorAll('input[type="checkbox"]').length : 0;

        let text;
        if (selectedCount === 0) {
            text = `Keine ${this.getFilterDisplayName(filterType)}`;
        } else if (selectedCount === totalOptions && totalOptions > 0) {
            text = `Alle ${this.getFilterDisplayName(filterType)}`;
        } else {
            text = `${selectedCount} ${this.getFilterDisplayName(filterType)}`;
        }

        // Desktop Button aktualisieren
        const desktopBtn = document.getElementById(`${filterType}-filter-btn`);
        if (desktopBtn) {
            const textElement = desktopBtn.querySelector('.filter-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }

        // Mobile Button aktualisieren
        const mobileBtn = document.getElementById(`mobile-${filterType}-filter-btn`);
        if (mobileBtn) {
            const textElement = mobileBtn.querySelector('.filter-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }
    }

    getFilterDisplayName(filterType) {
        const displayNames = {
            genre: 'Genres',
            sprache: 'Sprachen',
            album: 'Alben'
        };
        return displayNames[filterType] || filterType;
    }

    toggleFilterDropdown(dropdown) {
        // Aktuelles Dropdown öffnen/schließen
        const content = dropdown.querySelector('.filter-dropdown-content');
        const isCurrentlyOpen = content.style.display === 'block';

        if (isCurrentlyOpen) {
            // Dropdown schließen (OHNE Filter anzuwenden)
            content.style.display = 'none';
        } else {
            // Alle anderen Dropdowns schließen (OHNE Filter anzuwenden)
            this.closeAllFilterDropdowns(false);
            // Aktuelles Dropdown öffnen
            content.style.display = 'block';
        }

        // Arrow-Icon drehen
        const arrow = dropdown.querySelector('.filter-arrow');
        if (arrow) {
            arrow.style.transform = isCurrentlyOpen ? 'rotate(0deg)' : 'rotate(180deg)';
        }
    }

    closeAllFilterDropdowns(applyFilters = false) {
        // Alle Dropdowns schließen
        document.querySelectorAll('.filter-dropdown-content').forEach(content => {
            content.style.display = 'none';
        });

        // Alle Pfeile zurücksetzen
        document.querySelectorAll('.filter-arrow').forEach(arrow => {
            arrow.style.transform = 'rotate(0deg)';
        });

        // Filter nur anwenden wenn explizit gewünscht
        if (applyFilters) {
            this.applyFilters();
        }
    }

    updateAllFilterCheckboxes() {
        const filterTypes = ['genre', 'sprache', 'album'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);
                if (optionsContainer) {
                    // Alle Checkboxen (inklusive "Alle [Kategorie]") setzen
                    const checkboxes = optionsContainer.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        if (checkbox.value === 'alle') {
                            // "Alle [Kategorie]" Checkbox: aktiviert wenn alle anderen aktiviert sind
                            const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');
                            const allIndividualSelected = Array.from(individualCheckboxes).every(cb =>
                                this.selectedFilters[filterType].has(cb.value)
                            );
                            checkbox.checked = allIndividualSelected;
                        } else {
                            // Individuelle Checkboxen
                            checkbox.checked = this.selectedFilters[filterType].has(checkbox.value);
                        }
                    });
                }
            });
        });

        // Button-Texte aktualisieren
        filterTypes.forEach(filterType => {
            this.updateFilterButtonText(filterType);
        });
    }

    syncAllCategoryCheckboxes() {
        const filterTypes = ['genre', 'sprache', 'album'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);
                if (optionsContainer) {
                    const allCheckbox = optionsContainer.querySelector('input[value="alle"]');
                    const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');

                    if (allCheckbox && individualCheckboxes.length > 0) {
                        // Prüfen ob alle individuellen Checkboxen aktiviert sind
                        const allIndividualChecked = Array.from(individualCheckboxes).every(cb => cb.checked);
                        allCheckbox.checked = allIndividualChecked;
                    }
                }
            });
        });
    }



    setupLegacyFilters() {
        // Fallback für alte Filter-Struktur
        console.log('Setting up legacy filters');

        // Alle Filter als ausgewählt setzen (zeigt alle Lieder)
        const genres = [...new Set(this.songs.map(song => song.genre))];
        const sprachen = [...new Set(this.songs.map(song => song.sprache))];
        const albums = [...new Set(this.songs.map(song => song.album))];

        this.selectedFilters.genre = new Set(genres);
        this.selectedFilters.sprache = new Set(sprachen);
        this.selectedFilters.album = new Set(albums);

        // Gefilterte Songs initial auf alle Songs setzen
        this.filteredSongs = [...this.songs];

        // Sofort rendern
        this.renderSongTable();
        this.renderMobileSongList();
    }



    setupFilterEventListeners() {
        // Dropdown-Buttons
        const filterTypes = ['genre', 'sprache', 'album'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const btn = document.getElementById(`${prefix}${filterType}-filter-btn`);
                const dropdown = document.getElementById(`${prefix}${filterType}-filter-dropdown`);

                if (btn && dropdown) {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleFilterDropdown(dropdown);
                    });
                } else {
                    console.warn(`Filter elements not found: ${prefix}${filterType}-filter-btn or ${prefix}${filterType}-filter-dropdown`);
                }
            });
        });



        // Dropdown schließen wenn außerhalb geklickt wird
        document.addEventListener('click', () => {
            this.closeAllFilterDropdowns();
        });
    }





    updateFilterButtonText(filterType) {
        const selectedCount = this.selectedFilters[filterType].size;
        const totalOptionsElement = document.querySelector(`#${filterType}-filter-options`);
        const totalOptions = totalOptionsElement ?
            totalOptionsElement.querySelectorAll('input[type="checkbox"]').length : 0;

        let text;
        if (selectedCount === 0) {
            text = `Keine ${this.getFilterDisplayName(filterType)}`;
        } else if (selectedCount === totalOptions && totalOptions > 0) {
            text = `Alle ${this.getFilterDisplayName(filterType)}`;
        } else {
            text = `${selectedCount} ${this.getFilterDisplayName(filterType)}`;
        }

        // Desktop Button aktualisieren
        const desktopBtn = document.getElementById(`${filterType}-filter-btn`);
        if (desktopBtn) {
            const textElement = desktopBtn.querySelector('.filter-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }

        // Mobile Button aktualisieren
        const mobileBtn = document.getElementById(`mobile-${filterType}-filter-btn`);
        if (mobileBtn) {
            const textElement = mobileBtn.querySelector('.filter-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }
    }

    getFilterDisplayName(filterType) {
        const displayNames = {
            genre: 'Genres',
            sprache: 'Sprachen',
            album: 'Alben'
        };
        return displayNames[filterType] || filterType;
    }

    toggleFilterDropdown(dropdown) {
        // Alle anderen Dropdowns schließen
        this.closeAllFilterDropdowns();

        // Aktuelles Dropdown öffnen/schließen
        const content = dropdown.querySelector('.filter-dropdown-content');
        const isOpen = content.style.display === 'block';
        content.style.display = isOpen ? 'none' : 'block';

        // Arrow-Icon drehen
        const arrow = dropdown.querySelector('.filter-arrow');
        arrow.style.transform = isOpen ? 'rotate(0deg)' : 'rotate(180deg)';
    }

    closeAllFilterDropdowns() {
        document.querySelectorAll('.filter-dropdown-content').forEach(content => {
            content.style.display = 'none';
        });
        document.querySelectorAll('.filter-arrow').forEach(arrow => {
            arrow.style.transform = 'rotate(0deg)';
        });
    }

    updateAllFilterCheckboxes() {
        const filterTypes = ['genre', 'sprache', 'album'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);
                if (optionsContainer) {
                    const checkboxes = optionsContainer.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.selectedFilters[filterType].has(checkbox.value);
                    });
                } else {
                    console.warn(`Options container not found: ${prefix}${filterType}-filter-options`);
                }
            });
        });

        // Button-Texte aktualisieren
        filterTypes.forEach(filterType => {
            this.updateFilterButtonText(filterType);
        });
    }
    
    applyFilters() {
        // Sicherheitsüberprüfung
        if (!this.songs || this.songs.length === 0) {
            return;
        }

        // Multiple Choice Filter anwenden - wenn nichts ausgewählt ist, nichts anzeigen
        this.filteredSongs = this.songs.filter(song => {
            const genreMatch = this.selectedFilters.genre.size > 0 && this.selectedFilters.genre.has(song.genre);
            const spracheMatch = this.selectedFilters.sprache.size > 0 && this.selectedFilters.sprache.has(song.sprache);
            const albumMatch = this.selectedFilters.album.size > 0 && this.selectedFilters.album.has(song.album);

            return genreMatch && spracheMatch && albumMatch;
        });

        // Shuffle-Queue zurücksetzen wenn Filter geändert werden
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        // Prüfen ob aktuelles Lied noch verfügbar ist
        this.handleCurrentSongNotInFilter();

        // UI aktualisieren
        this.renderSongTable();
        this.renderMobileSongList();
    }
    
    resetFilters() {
        // Alle Filter zurücksetzen
        const genres = [...new Set(this.songs.map(song => song.genre))];
        const sprachen = [...new Set(this.songs.map(song => song.sprache))];
        const albums = [...new Set(this.songs.map(song => song.album))];

        this.selectedFilters.genre = new Set(genres);
        this.selectedFilters.sprache = new Set(sprachen);
        this.selectedFilters.album = new Set(albums);

        // Alle Checkboxen aktivieren
        this.updateAllFilterCheckboxes();

        // Gefilterte Songs zurücksetzen
        this.filteredSongs = [...this.songs];

        // Shuffle-Queue zurücksetzen wenn Filter zurückgesetzt werden
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        // Prüfen ob aktuelles Lied noch verfügbar ist (sollte jetzt wieder verfügbar sein)
        this.handleCurrentSongNotInFilter();

        this.renderSongTable();
        this.renderMobileSongList();
    }

    toggleMobileFilters() {
        const filterControls = document.getElementById('mobile-filter-controls');
        const toggleBtn = document.getElementById('mobile-filter-toggle');

        if (filterControls.style.display === 'none') {
            filterControls.style.display = 'flex';
            toggleBtn.classList.add('active');
        } else {
            filterControls.style.display = 'none';
            toggleBtn.classList.remove('active');
        }
    }

    toggleMobileMenu(songId) {
        const dropdown = document.querySelector(`.mobile-menu-dropdown[data-song-id="${songId}"]`);

        // Prüfen ob das Menü bereits geöffnet ist
        if (dropdown && dropdown.classList.contains('show')) {
            // Menü schließen wenn es bereits offen ist
            dropdown.classList.remove('show');
        } else {
            // Alle anderen Menüs schließen
            this.closeMobileMenus();

            // Das gewünschte Menü öffnen
            if (dropdown) {
                dropdown.classList.add('show');
            }
        }
    }

    closeMobileMenus() {
        const allDropdowns = document.querySelectorAll('.mobile-menu-dropdown');
        allDropdowns.forEach(dropdown => {
            dropdown.classList.remove('show');
        });
    }

    handleMobileMenuAction(action, songId) {
        const song = this.songs.find(s => s.id === songId);
        if (!song) return;

        switch (action) {
            case 'queue':
                this.addToQueue(songId);
                break;
            case 'hide':
                this.toggleSongVisibility(songId);
                break;
            case 'download':
                this.downloadSong(song);
                break;
        }
    }

    handleDesktopMenuAction(action, songId) {
        const song = this.songs.find(s => s.id === songId);
        if (!song) return;

        switch (action) {
            case 'queue':
                this.addToQueue(songId);
                break;
            case 'hide':
                this.toggleSongVisibility(songId);
                break;
            case 'download':
                this.downloadSong(songId);
                break;
        }
    }

    addToQueue(songId) {
        const song = this.songs.find(s => s.id === songId);
        if (!song) return;

        // Prüfen ob Lied bereits in der Queue ist
        if (!this.playQueue.find(queueSong => queueSong.id === songId)) {
            this.playQueue.push(song);
            this.showQueueNotification(`"${song.titel}" zur Warteschlange hinzugefügt`);
        } else {
            this.showQueueNotification(`"${song.titel}" ist bereits in der Warteschlange`);
        }
    }

    showQueueNotification(message) {
        // Temporäre Benachrichtigung anzeigen
        const notification = document.createElement('div');
        notification.className = 'queue-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4b879a;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Animation einblenden
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Nach 3 Sekunden ausblenden
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    clearFiltersAndQueue() {
        // Filter zurücksetzen
        this.resetFilters();

        // Warteschlange leeren
        this.playQueue = [];

        // Filter anwenden um UI zu aktualisieren
        this.applyFilters();

        this.showQueueNotification('Filter und Warteschlange zurückgesetzt');
    }

    toggleDesktopMenu(songId) {
        const dropdown = document.querySelector(`.desktop-menu-dropdown[data-song-id="${songId}"]`);

        // Alle anderen Menüs schließen
        this.closeDesktopMenus();

        // Aktuelles Menü öffnen/schließen
        if (dropdown) {
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }
    }

    closeDesktopMenus() {
        const allDropdowns = document.querySelectorAll('.desktop-menu-dropdown');
        allDropdowns.forEach(dropdown => {
            dropdown.style.display = 'none';
        });
    }

    filterByCurrentAlbum() {
        if (this.currentSongIndex === -1) return;

        // Album-Filter auf das aktuelle Album setzen
        const currentSong = this.songs[this.currentSongIndex];
        const albumName = currentSong.album;

        // Alle Filter zurücksetzen
        document.getElementById('genre-filter').value = 'alle';
        document.getElementById('sprache-filter').value = 'alle';
        document.getElementById('mobile-genre-filter').value = 'alle';
        document.getElementById('mobile-sprache-filter').value = 'alle';
        document.getElementById('mobile-album-filter').value = albumName.toLowerCase();

        // Nach Album filtern
        this.filteredSongs = this.songs.filter(song => song.album === albumName);

        // Shuffle-Queue zurücksetzen wenn nach Album gefiltert wird
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        // Prüfen ob aktuelles Lied noch verfügbar ist
        this.handleCurrentSongNotInFilter();

        this.renderSongTable();
        this.renderMobileSongList();

        // Visuelles Feedback
        const albumElement = document.getElementById('current-album');
        albumElement.style.color = '#112736';
        albumElement.style.fontWeight = 'bold';

        setTimeout(() => {
            albumElement.style.color = '';
            albumElement.style.fontWeight = '';
        }, 1000);
    }
    
    sortTable(column) {
        if (this.currentSort.column === column) {
            this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort.column = column;
            this.currentSort.direction = 'asc';
        }
        
        this.filteredSongs.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            if (column === 'nummer') {
                aVal = parseInt(aVal);
                bVal = parseInt(bVal);
            } else {
                aVal = aVal.toString().toLowerCase();
                bVal = bVal.toString().toLowerCase();
            }

            if (aVal < bVal) return this.currentSort.direction === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.currentSort.direction === 'asc' ? 1 : -1;
            return 0;
        });

        // Shuffle-Queue zurücksetzen wenn sortiert wird
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        this.updateSortIndicators();
        this.renderSongTable();
        this.renderMobileSongList();
    }
    
    updateSortIndicators() {
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.className = 'sort-indicator';
        });
        
        const activeHeader = document.querySelector(`[data-column="${this.currentSort.column}"] .sort-indicator`);
        if (activeHeader) {
            activeHeader.className = `sort-indicator ${this.currentSort.direction}`;
        }
    }

    safeMobileSongSelection(index) {
        // Verhindert schnelles Durchschalten von Songs
        if (this.isChangingSong) {
            return;
        }

        // Debouncing: Vorherige Auswahl abbrechen
        if (this.songSelectionTimeout) {
            clearTimeout(this.songSelectionTimeout);
        }

        this.isChangingSong = true;

        // Song nach kurzer Verzögerung abspielen
        this.songSelectionTimeout = setTimeout(() => {
            this.playSong(index);

            // Modal nach erfolgreichem Song-Start öffnen
            setTimeout(() => {
                if (this.currentSongIndex === index && !this.audio.error) {
                    this.openMobilePlayer();
                }
                this.isChangingSong = false;
            }, 100);
        }, 150);
    }

    playSong(index) {
        if (index < 0 || index >= this.songs.length) return;

        this.currentSongIndex = index;
        const song = this.songs[index];

        // Audio laden
        this.audio.src = `audio/${song.dateiname}`;
        this.audio.load();

        // UI aktualisieren
        this.updatePlayerInfo(song);
        this.updatePlayingState();

        // Abspielen
        this.audio.play().then(() => {
            // isPlaying wird durch 'play' Event gesetzt
            // Mini-Player anzeigen wenn im mobilen Layout und Modal nicht geöffnet
            if (window.innerWidth <= 768 && this.modalOverlay.style.display !== 'flex') {
                this.miniPlayer.style.display = 'flex';
            }
        }).catch(error => {
            console.error('Fehler beim Abspielen:', error);
            this.handleAudioError(error);
        });
    }

    // Hilfsfunktion: Spielt ein Lied basierend auf der gefilterten Liste
    playSongFromFiltered(filteredIndex) {
        if (filteredIndex < 0 || filteredIndex >= this.filteredSongs.length) return;

        const song = this.filteredSongs[filteredIndex];
        const originalIndex = this.songs.findIndex(s => s.id === song.id);

        if (originalIndex !== -1) {
            this.playSong(originalIndex);
        }
    }

    // Hilfsfunktion: Findet den Index eines Songs in der gefilterten Liste
    getCurrentFilteredIndex() {
        if (this.currentSongIndex === -1) return -1;

        const currentSong = this.songs[this.currentSongIndex];
        return this.filteredSongs.findIndex(song => song.id === currentSong.id);
    }

    // Hilfsfunktion: Prüft ob das aktuelle Lied noch in der gefilterten Liste ist
    isCurrentSongInFilteredList() {
        return this.getCurrentFilteredIndex() !== -1;
    }

    // Hilfsfunktion: Behandelt Situationen, wo das aktuelle Lied nicht mehr verfügbar ist
    handleCurrentSongNotInFilter() {
        if (this.currentSongIndex !== -1 && this.isPlaying && !this.isCurrentSongInFilteredList()) {
            // Aktuelles Lied ist nicht mehr in der gefilterten Liste
            if (this.filteredSongs.length > 0) {
                // Zum ersten verfügbaren Lied wechseln
                this.playSongFromFiltered(0);
            } else {
                // Keine Lieder verfügbar - Player stoppen
                this.audio.pause();
                // isPlaying wird durch 'pause' Event gesetzt
            }
        }
    }

    updatePlayerInfo(song) {
        this.currentSongTitle.textContent = song.titel;
        this.currentAlbum.textContent = song.album;
        this.miniSongTitle.textContent = song.titel;

        // Album Cover laden
        this.loadAlbumCover(song.nummer);

        // Lyrics und Übersetzung anzeigen
        this.updateLyricsDisplay(song);

        // Modal auch aktualisieren wenn geöffnet
        this.updateModalPlayerInfo(song);

        // Media Session für Sperrbildschirm/Benachrichtigungen aktualisieren
        this.updateMediaSession(song);
    }

    updateModalPlayerInfo(song) {
        // Nur aktualisieren wenn Modal geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        // Song-Titel aktualisieren
        const modalSongTitle = document.getElementById('modal-song-title');
        if (modalSongTitle) {
            modalSongTitle.textContent = song.titel;
        }

        // Artist aktualisieren
        const modalArtist = document.getElementById('modal-artist');
        if (modalArtist) {
            modalArtist.textContent = 'Jana Breitmar';
        }

        // Album aktualisieren
        const modalAlbumName = document.getElementById('modal-album-name');
        if (modalAlbumName) {
            modalAlbumName.textContent = song.album;
        }

        // Album Cover als Hintergrund des Cover-Tabs setzen
        this.loadModalAlbumCoverBackground(song.nummer);

        // Info-Tab aktualisieren
        this.updateModalInfoTab(song);

        // Lyrics und Translation Tabs aktualisieren
        this.updateModalLyricsTabs(song);

        // Lyrics aktualisieren
        this.updateModalLyrics(song);
    }

    updateModalLyrics(song) {
        // Nur aktualisieren wenn Modal geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        const modalContent = document.querySelector('.modal-content');
        if (!modalContent) return;

        // Lyrics Content aktualisieren
        const modalLyricsContent = modalContent.querySelector('#lyrics-content');
        if (modalLyricsContent) {
            if (song.lyrics && song.lyrics.trim()) {
                let formattedLyrics = this.formatLyrics(song.lyrics);
                modalLyricsContent.innerHTML = `<p>${formattedLyrics}</p>`;
            } else {
                modalLyricsContent.innerHTML = '<p class="no-lyrics">Keine Lyrics verfügbar für dieses Lied.</p>';
            }
        }

        // Lyrics Translation aktualisieren
        const modalLyricsTranslation = modalContent.querySelector('#lyrics-translation');
        if (modalLyricsTranslation) {
            if (this.hasTranslation(song)) {
                let formattedTranslation = this.formatLyrics(song.translation);
                modalLyricsTranslation.innerHTML = `<p>${formattedTranslation}</p>`;
            } else {
                modalLyricsTranslation.innerHTML = '<p class="no-lyrics">Keine deutsche Übersetzung verfügbar.</p>';
            }
        }

        // Lyrics Info aktualisieren
        const modalLyricsInfo = modalContent.querySelector('#lyrics-info');
        if (modalLyricsInfo) {
            if (this.hasInfo(song)) {
                let formattedInfo = this.formatLyrics(song.info);
                modalLyricsInfo.innerHTML = `<p>${formattedInfo}</p>`;
            } else {
                modalLyricsInfo.innerHTML = '<p class="no-lyrics">Keine zusätzlichen Informationen verfügbar.</p>';
            }
        }

        // Tabs im Modal konfigurieren
        this.configureModalTabs(song);
    }

    configureModalTabs(song) {
        // Nur aktualisieren wenn Modal geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        const modalContent = document.querySelector('.modal-content');
        if (!modalContent) return;

        const hasTranslation = this.hasTranslation(song);
        const hasInfo = this.hasInfo(song);

        // Tab-Buttons im Modal anzeigen/verstecken
        const modalTranslationTab = modalContent.querySelector('[data-tab="translation"]');
        const modalInfoTab = modalContent.querySelector('[data-tab="info"]');

        if (modalTranslationTab) {
            if (hasTranslation) {
                modalTranslationTab.style.display = 'block';
            } else {
                modalTranslationTab.style.display = 'none';
            }
        }

        if (modalInfoTab) {
            if (hasInfo) {
                modalInfoTab.style.display = 'block';
            } else {
                modalInfoTab.style.display = 'none';
            }
        }

        // Lyrics Tabs im Modal sichtbar machen
        const modalLyricsTabs = modalContent.querySelector('#lyrics-tabs');
        if (modalLyricsTabs) {
            modalLyricsTabs.style.display = 'flex';
        }

        // Standard: Lyrics Tab aktiv im Modal
        this.switchModalLyricsTab('lyrics');
    }

    switchModalLyricsTab(tabType) {
        // Nur aktualisieren wenn Modal geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        const modalContent = document.querySelector('.modal-content');
        if (!modalContent) return;

        // Tab-Buttons im Modal aktualisieren
        modalContent.querySelectorAll('.lyrics-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const modalTargetTab = modalContent.querySelector(`[data-tab="${tabType}"]`);
        if (modalTargetTab) {
            modalTargetTab.classList.add('active');
        }

        // Alle Content-Container im Modal verstecken
        const modalLyricsContent = modalContent.querySelector('#lyrics-content');
        const modalLyricsTranslation = modalContent.querySelector('#lyrics-translation');
        const modalLyricsInfo = modalContent.querySelector('#lyrics-info');

        if (modalLyricsContent) modalLyricsContent.style.display = 'none';
        if (modalLyricsTranslation) modalLyricsTranslation.style.display = 'none';
        if (modalLyricsInfo) modalLyricsInfo.style.display = 'none';

        // Gewählten Content im Modal anzeigen
        switch(tabType) {
            case 'lyrics':
                if (modalLyricsContent) modalLyricsContent.style.display = 'block';
                break;
            case 'translation':
                if (modalLyricsTranslation) modalLyricsTranslation.style.display = 'block';
                break;
            case 'info':
                if (modalLyricsInfo) modalLyricsInfo.style.display = 'block';
                break;
        }
    }

    loadAlbumCover(songNumber) {
        const albumCover = document.getElementById('album-cover');
        const miniAlbumCover = document.querySelector('.mini-album-cover');

        // Verschiedene Bildformate versuchen
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                this.showPlaceholderCover(albumCover, miniAlbumCover);
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverPath = `bilder/album-covers/${songNumber}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - anzeigen
                this.showAlbumCover(albumCover, miniAlbumCover, coverPath);
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    showAlbumCover(albumCover, miniAlbumCover, coverPath) {
        // Hauptplayer Album-Cover
        albumCover.style.backgroundImage = `url('${coverPath}')`;
        albumCover.style.backgroundSize = 'cover';
        albumCover.style.backgroundPosition = 'center';
        albumCover.style.backgroundRepeat = 'no-repeat';
        albumCover.innerHTML = '';
        albumCover.classList.add('has-cover');

        // Mini-Player Album-Cover
        if (miniAlbumCover) {
            miniAlbumCover.style.backgroundImage = `url('${coverPath}')`;
            miniAlbumCover.style.backgroundSize = 'cover';
            miniAlbumCover.style.backgroundPosition = 'center';
            miniAlbumCover.style.backgroundRepeat = 'no-repeat';
            miniAlbumCover.innerHTML = '';
            miniAlbumCover.classList.add('has-cover');
        }

        // Media Session mit korrektem Cover aktualisieren
        this.currentCoverPath = coverPath;
        if (this.currentSongIndex !== -1) {
            this.updateMediaSessionWithCover(this.songs[this.currentSongIndex], coverPath);
        }
    }

    showPlaceholderCover(albumCover, miniAlbumCover) {
        // Hauptplayer Platzhalter
        albumCover.style.backgroundImage = '';
        albumCover.classList.remove('has-cover');
        albumCover.innerHTML = `
            <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
            </svg>
        `;

        // Mini-Player Platzhalter
        if (miniAlbumCover) {
            miniAlbumCover.style.backgroundImage = '';
            miniAlbumCover.classList.remove('has-cover');
            miniAlbumCover.innerHTML = `
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                </svg>
            `;
        }

        // Media Session ohne Cover aktualisieren (Fallback auf Favicon)
        this.currentCoverPath = null;
    }

    updateMediaSession(song) {
        // Media Session API für Sperrbildschirm/Benachrichtigungen
        if ('mediaSession' in navigator) {
            // Action Handlers nur einmal setzen
            this.setupMediaSessionHandlers();

            // Song-Metadaten setzen
            const artwork = this.currentCoverPath ?
                this.createArtworkArray(this.currentCoverPath) :
                this.getMediaSessionArtwork(song.nummer);

            navigator.mediaSession.metadata = new MediaMetadata({
                title: song.titel,
                artist: 'Jana Breitmar',
                album: song.album,
                artwork: artwork
            });
        }
    }

    setupMediaSessionHandlers() {
        // Action Handlers nur einmal setzen (vermeidet mehrfache Registrierung)
        if (this.mediaSessionHandlersSet) return;

        navigator.mediaSession.setActionHandler('play', () => {
            this.audio.play();
            // isPlaying wird durch 'play' Event gesetzt
        });

        navigator.mediaSession.setActionHandler('pause', () => {
            this.audio.pause();
            // isPlaying wird durch 'pause' Event gesetzt
        });

        navigator.mediaSession.setActionHandler('previoustrack', () => {
            this.previousSong();
        });

        navigator.mediaSession.setActionHandler('nexttrack', () => {
            this.nextSong();
        });

        // Seek-Funktionalität (optional)
        navigator.mediaSession.setActionHandler('seekto', (details) => {
            if (details.seekTime && this.audio.duration) {
                this.audio.currentTime = details.seekTime;
            }
        });

        this.mediaSessionHandlersSet = true;
    }

    createArtworkArray(coverPath) {
        // Artwork Array für spezifischen Cover-Pfad erstellen
        return [
            { src: coverPath, sizes: '96x96', type: 'image/png' },
            { src: coverPath, sizes: '128x128', type: 'image/png' },
            { src: coverPath, sizes: '192x192', type: 'image/png' },
            { src: coverPath, sizes: '256x256', type: 'image/png' },
            { src: coverPath, sizes: '384x384', type: 'image/png' },
            { src: coverPath, sizes: '512x512', type: 'image/png' }
        ];
    }

    updateMediaSessionPosition() {
        // Media Session Position für Sperrbildschirm aktualisieren
        if ('mediaSession' in navigator && 'setPositionState' in navigator.mediaSession) {
            if (this.audio.duration && !isNaN(this.audio.duration)) {
                navigator.mediaSession.setPositionState({
                    duration: this.audio.duration,
                    playbackRate: this.audio.playbackRate,
                    position: this.audio.currentTime
                });
            }
        }
    }

    updateMediaSessionPlaybackState() {
        // Media Session Playback State aktualisieren
        if ('mediaSession' in navigator) {
            navigator.mediaSession.playbackState = this.isPlaying ? 'playing' : 'paused';
        }
    }

    getMediaSessionArtwork(songNumber) {
        // Album-Cover URLs für Media Session generieren
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        const artworkArray = [];

        possibleFormats.forEach(format => {
            const coverPath = `bilder/album-covers/${songNumber}.${format}`;
            // Verschiedene Größen für bessere Kompatibilität
            artworkArray.push(
                { src: coverPath, sizes: '96x96', type: `image/${format}` },
                { src: coverPath, sizes: '128x128', type: `image/${format}` },
                { src: coverPath, sizes: '192x192', type: `image/${format}` },
                { src: coverPath, sizes: '256x256', type: `image/${format}` },
                { src: coverPath, sizes: '384x384', type: `image/${format}` },
                { src: coverPath, sizes: '512x512', type: `image/${format}` }
            );
        });

        return artworkArray;
    }

    updateMediaSessionWithCover(song, coverPath) {
        // Media Session mit spezifischem Cover-Pfad aktualisieren
        if ('mediaSession' in navigator) {
            navigator.mediaSession.metadata = new MediaMetadata({
                title: song.titel,
                artist: 'Jana Breitmar',
                album: song.album,
                artwork: [
                    { src: coverPath, sizes: '96x96', type: 'image/png' },
                    { src: coverPath, sizes: '128x128', type: 'image/png' },
                    { src: coverPath, sizes: '192x192', type: 'image/png' },
                    { src: coverPath, sizes: '256x256', type: 'image/png' },
                    { src: coverPath, sizes: '384x384', type: 'image/png' },
                    { src: coverPath, sizes: '512x512', type: 'image/png' }
                ]
            });
        }
    }

    loadMobileAlbumCover(mobileAlbumCover, songNumber) {
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                mobileAlbumCover.style.backgroundImage = '';
                mobileAlbumCover.classList.remove('has-cover');
                mobileAlbumCover.innerHTML = `
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                `;
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverPath = `bilder/album-covers/${songNumber}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - anzeigen
                mobileAlbumCover.style.backgroundImage = `url('${coverPath}')`;
                mobileAlbumCover.style.backgroundSize = 'cover';
                mobileAlbumCover.style.backgroundPosition = 'center';
                mobileAlbumCover.style.backgroundRepeat = 'no-repeat';
                mobileAlbumCover.innerHTML = '';
                mobileAlbumCover.classList.add('has-cover');
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    loadModalAlbumCover(modalAlbumCover, songNumber) {
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                modalAlbumCover.style.backgroundImage = '';
                modalAlbumCover.classList.remove('has-cover');
                modalAlbumCover.innerHTML = `
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                `;
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverPath = `bilder/album-covers/${songNumber}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - anzeigen
                modalAlbumCover.style.backgroundImage = `url('${coverPath}')`;
                modalAlbumCover.style.backgroundSize = 'cover';
                modalAlbumCover.style.backgroundPosition = 'center';
                modalAlbumCover.style.backgroundRepeat = 'no-repeat';
                modalAlbumCover.innerHTML = '';
                modalAlbumCover.classList.add('has-cover');
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    loadModalAlbumCoverBackground(songNumber) {
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;
        const coverTab = document.querySelector('.modal-tab-content[data-content="cover"]');

        if (!coverTab) return;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                coverTab.style.backgroundImage = '';
                coverTab.classList.remove('has-cover');
                coverTab.innerHTML = `
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                `;
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverPath = `bilder/album-covers/${songNumber}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - als Hintergrund setzen
                coverTab.style.backgroundImage = `url('${coverPath}')`;
                coverTab.classList.add('has-cover');
                coverTab.innerHTML = '';
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    updatePlayingState() {
        // Alle Zeilen zurücksetzen
        document.querySelectorAll('.song-table tbody tr').forEach(row => {
            row.classList.remove('playing');
        });
        document.querySelectorAll('.mobile-song-item').forEach(item => {
            item.classList.remove('playing');
        });

        // Aktuelle Zeile markieren
        const currentRow = document.querySelector(`[data-song-id="${this.songs[this.currentSongIndex].id}"]`);
        if (currentRow) {
            currentRow.classList.add('playing');
        }
    }

    togglePlayPause() {
        if (this.currentSongIndex === -1) {
            this.playSong(0);
            return;
        }

        if (this.isPlaying) {
            this.audio.pause();
            // isPlaying wird durch 'pause' Event gesetzt
        } else {
            this.audio.play().catch(error => {
                console.error('Fehler beim Abspielen:', error);
            });
            // isPlaying wird durch 'play' Event gesetzt
        }
    }

    updatePlayButton() {
        const playIcons = document.querySelectorAll('.play-icon');
        const pauseIcons = document.querySelectorAll('.pause-icon');

        if (this.isPlaying) {
            playIcons.forEach(icon => icon.style.display = 'none');
            pauseIcons.forEach(icon => icon.style.display = 'block');
        } else {
            playIcons.forEach(icon => icon.style.display = 'block');
            pauseIcons.forEach(icon => icon.style.display = 'none');
        }

        // Modal Button Zustand auch aktualisieren
        this.updateModalButtonStates();
    }

    previousSong() {
        // Prüfen ob gefilterte Liste leer ist
        if (this.filteredSongs.length === 0) {
            return;
        }

        if (this.isShuffleMode) {
            const previousIndex = this.getPreviousShuffleSong();
            this.playSong(previousIndex);
        } else {
            const currentFilteredIndex = this.getCurrentFilteredIndex();
            if (currentFilteredIndex <= 0) {
                // Zum letzten Lied in der gefilterten Liste
                this.playSongFromFiltered(this.filteredSongs.length - 1);
            } else {
                // Zum vorherigen Lied in der gefilterten Liste
                this.playSongFromFiltered(currentFilteredIndex - 1);
            }
        }
    }

    nextSong() {
        // Prüfen ob gefilterte Liste leer ist
        if (this.filteredSongs.length === 0) {
            return;
        }

        // Single Repeat: Aktuelles Lied wiederholen
        if (this.repeatMode === 'single' && this.currentSongIndex !== -1) {
            this.playSong(this.currentSongIndex);
            return;
        }

        // Warteschlange prüfen (hat Priorität vor Shuffle/Normal)
        if (this.playQueue.length > 0) {
            const nextSong = this.playQueue.shift(); // Erstes Lied aus Queue entfernen
            const songIndex = this.songs.findIndex(song => song.id === nextSong.id);
            if (songIndex !== -1) {
                this.playSong(songIndex);
                return;
            }
        }

        if (this.isShuffleMode) {
            const nextIndex = this.getNextShuffleSong();
            if (nextIndex !== -1) {
                this.playSong(nextIndex);
            }
        } else {
            const currentFilteredIndex = this.getCurrentFilteredIndex();
            if (currentFilteredIndex >= this.filteredSongs.length - 1) {
                // Am Ende der Playlist
                if (this.repeatMode === 'playlist') {
                    // Playlist wiederholen: Zum ersten Lied
                    this.playSongFromFiltered(0);
                } else {
                    // Kein Repeat: Stoppen
                    this.audio.pause();
                    // isPlaying wird durch 'pause' Event gesetzt
                }
            } else {
                // Zum nächsten Lied in der gefilterten Liste
                this.playSongFromFiltered(currentFilteredIndex + 1);
            }
        }
    }

    toggleShuffle() {
        this.isShuffleMode = !this.isShuffleMode;
        const shuffleBtn = document.getElementById('shuffle-btn');
        const mobileShuffleBtn = document.getElementById('mobile-shuffle-btn');
        shuffleBtn.classList.toggle('active', this.isShuffleMode);
        mobileShuffleBtn.classList.toggle('active', this.isShuffleMode);

        // Modal Button Zustand auch aktualisieren
        this.updateModalButtonStates();

        // Shuffle-Queue zurücksetzen wenn Shuffle aktiviert wird
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }
    }

    // Shuffle-Queue verwalten
    resetShuffleQueue() {
        // Alle verfügbaren (nicht versteckten) Lieder aus der gefilterten Liste
        const availableSongs = this.filteredSongs.filter(song => !song.isHidden);
        this.shuffleQueue = [...availableSongs];
        this.shuffleHistory = [];

        // Aktuelles Lied aus der Queue entfernen, falls es gespielt wird
        if (this.currentSongIndex !== -1) {
            const currentSong = this.songs[this.currentSongIndex];
            const currentInQueue = this.shuffleQueue.findIndex(song => song.id === currentSong.id);
            if (currentInQueue !== -1) {
                this.shuffleQueue.splice(currentInQueue, 1);
                this.shuffleHistory.push(currentSong);
            }
        }
    }

    getNextShuffleSong() {
        // Wenn Queue leer ist, alle Lieder wieder hinzufügen (außer dem aktuellen)
        if (this.shuffleQueue.length === 0) {
            this.resetShuffleQueue();
        }

        // Wenn immer noch leer (nur ein Lied verfügbar oder keine verfügbaren Lieder), das aktuelle Lied wiederholen
        if (this.shuffleQueue.length === 0) {
            if (this.currentSongIndex !== -1 && this.isCurrentSongInFilteredList()) {
                return this.currentSongIndex;
            } else {
                // Kein verfügbares Lied
                return -1;
            }
        }

        // Zufälliges Lied aus der Queue wählen
        const randomIndex = Math.floor(Math.random() * this.shuffleQueue.length);
        const selectedSong = this.shuffleQueue[randomIndex];

        // Lied aus Queue entfernen und zur History hinzufügen
        this.shuffleQueue.splice(randomIndex, 1);
        this.shuffleHistory.push(selectedSong);

        // Original-Index des gewählten Liedes finden
        return this.songs.findIndex(song => song.id === selectedSong.id);
    }

    getPreviousShuffleSong() {
        if (this.shuffleHistory.length <= 1) {
            return this.currentSongIndex; // Kein vorheriges Lied verfügbar
        }

        // Aktuelles Lied zurück in die Queue
        const currentSong = this.shuffleHistory.pop();
        if (currentSong) {
            this.shuffleQueue.unshift(currentSong);
        }

        // Vorheriges Lied aus der History
        const previousSong = this.shuffleHistory[this.shuffleHistory.length - 1];
        return this.songs.findIndex(song => song.id === previousSong.id);
    }

    toggleRepeat() {
        // Durch die drei Modi zyklieren: none -> playlist -> single -> none
        switch (this.repeatMode) {
            case 'none':
                this.repeatMode = 'playlist';
                break;
            case 'playlist':
                this.repeatMode = 'single';
                break;
            case 'single':
                this.repeatMode = 'none';
                break;
        }

        this.updateRepeatButtons();
    }

    updateRepeatButtons() {
        const repeatBtn = document.getElementById('repeat-btn');
        const mobileRepeatBtn = document.getElementById('mobile-repeat-btn');

        // Alle Klassen entfernen
        repeatBtn.classList.remove('active', 'single-repeat');
        mobileRepeatBtn.classList.remove('active', 'single-repeat');

        // Entsprechende Klassen und Inhalte setzen
        switch (this.repeatMode) {
            case 'none':
                // Keine besonderen Klassen oder Inhalte
                this.setRepeatButtonContent(repeatBtn, false);
                this.setRepeatButtonContent(mobileRepeatBtn, false);
                break;
            case 'playlist':
                repeatBtn.classList.add('active');
                mobileRepeatBtn.classList.add('active');
                this.setRepeatButtonContent(repeatBtn, false);
                this.setRepeatButtonContent(mobileRepeatBtn, false);
                break;
            case 'single':
                repeatBtn.classList.add('active', 'single-repeat');
                mobileRepeatBtn.classList.add('active', 'single-repeat');
                this.setRepeatButtonContent(repeatBtn, true);
                this.setRepeatButtonContent(mobileRepeatBtn, true);
                break;
        }

        // Modal Button Zustand auch aktualisieren
        this.updateModalButtonStates();
    }

    setRepeatButtonContent(button, isSingle) {
        if (!button) return;

        const svg = button.querySelector('svg');
        if (!svg) return;

        if (isSingle) {
            // "1" Symbol hinzufügen
            if (!svg.querySelector('.repeat-one')) {
                const oneText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                oneText.setAttribute('x', '12');
                oneText.setAttribute('y', '16');
                oneText.setAttribute('text-anchor', 'middle');
                oneText.setAttribute('font-size', '8');
                oneText.setAttribute('font-weight', 'bold');
                oneText.setAttribute('fill', 'currentColor');
                oneText.setAttribute('class', 'repeat-one');
                oneText.textContent = '1';
                svg.appendChild(oneText);
            }
        } else {
            // "1" Symbol entfernen
            const oneText = svg.querySelector('.repeat-one');
            if (oneText) {
                oneText.remove();
            }
        }
    }

    updateProgress() {
        if (this.audio.duration) {
            const progress = (this.audio.currentTime / this.audio.duration) * 100;
            this.progressFill.style.width = `${progress}%`;

            this.currentTimeDisplay.textContent = this.formatTime(this.audio.currentTime);

            // Modal auch aktualisieren wenn geöffnet
            this.updateModalProgressIfOpen();
        }
    }

    updateDuration() {
        this.totalTimeDisplay.textContent = this.formatTime(this.audio.duration);
        this.updateModalDurationIfOpen();
    }

    updateModalProgressIfOpen() {
        // Nur Modal aktualisieren wenn es geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        if (!this.audio.duration) return;

        const progress = (this.audio.currentTime / this.audio.duration) * 100;
        const currentTimeText = this.formatTime(this.audio.currentTime);

        const modalProgressFill = document.getElementById('modal-progress-fill');
        const modalCurrentTime = document.getElementById('modal-current-time');

        if (modalProgressFill) {
            modalProgressFill.style.width = `${progress}%`;
        }
        if (modalCurrentTime) {
            modalCurrentTime.textContent = currentTimeText;
        }
    }

    updateModalDurationIfOpen() {
        // Nur Modal aktualisieren wenn es geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        const modalTotalTime = document.getElementById('modal-total-time');
        if (modalTotalTime) {
            modalTotalTime.textContent = this.formatTime(this.audio.duration);
        }
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    seekTo(event) {
        const progressContainer = event.currentTarget;
        const rect = progressContainer.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;

        if (this.audio.duration) {
            this.audio.currentTime = percentage * this.audio.duration;
        }
    }

    toggleSongVisibility(songId) {
        const song = this.songs.find(s => s.id === songId);
        if (song) {
            song.isHidden = !song.isHidden;

            // Shuffle-Queue zurücksetzen wenn Sichtbarkeit geändert wird
            if (this.isShuffleMode) {
                this.resetShuffleQueue();
            }

            // Prüfen ob aktuelles Lied noch verfügbar ist
            this.handleCurrentSongNotInFilter();

            this.renderSongTable();
            this.renderMobileSongList();
        }
    }

    toggleAllVisibility() {
        const allHidden = this.songs.every(song => song.isHidden);
        this.songs.forEach(song => {
            song.isHidden = !allHidden;
        });

        // Shuffle-Queue zurücksetzen wenn alle Lieder ein-/ausgeblendet werden
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        // Prüfen ob aktuelles Lied noch verfügbar ist
        this.handleCurrentSongNotInFilter();

        this.renderSongTable();
        this.renderMobileSongList();
    }

    downloadSong(songId) {
        const song = this.songs.find(s => s.id === songId);
        if (song) {
            const link = document.createElement('a');
            link.href = `audio/${song.dateiname}`;
            link.download = `${song.titel} - Jana Breitmar.wav`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    downloadCurrent() {
        if (this.currentSongIndex !== -1) {
            this.downloadSong(this.songs[this.currentSongIndex].id);
        }
    }

    downloadAllVisible() {
        const visibleSongs = this.filteredSongs.filter(song => !song.isHidden);

        if (visibleSongs.length === 0) {
            alert('Keine sichtbaren Lieder zum Herunterladen.');
            return;
        }

        // Sequenzieller Download mit kleiner Verzögerung
        visibleSongs.forEach((song, index) => {
            setTimeout(() => {
                this.downloadSong(song.id);
            }, index * 500); // 500ms Verzögerung zwischen Downloads
        });
    }

    openMobilePlayer() {
        this.modalOverlay.style.display = 'flex';
        this.miniPlayer.style.display = 'none'; // Mini-Player verstecken wenn Modal geöffnet wird

        // Aktuellen Player-Zustand synchronisieren
        if (this.currentSongIndex !== -1) {
            const song = this.songs[this.currentSongIndex];
            this.updateModalPlayerInfo(song);
            this.syncModalProgress();
        } else {
            // Auch wenn kein Song spielt, Cover für ersten Song laden
            if (this.songs.length > 0) {
                this.loadModalAlbumCoverBackground(this.songs[0].nummer);
            }
        }

        // Event Listeners für Modal einrichten
        this.setupModalEventListeners();

        // Tab-System initialisieren
        this.initializeModalTabs();
    }

    syncModalProgress() {
        // Synchronisiert Progress Bar und Zeit beim Öffnen des Modals
        if (!this.audio.duration) return;

        const progress = (this.audio.currentTime / this.audio.duration) * 100;
        const currentTimeText = this.formatTime(this.audio.currentTime);
        const durationText = this.formatTime(this.audio.duration);

        const modalProgressFill = document.getElementById('modal-progress-fill');
        const modalCurrentTime = document.getElementById('modal-current-time');
        const modalTotalTime = document.getElementById('modal-total-time');

        if (modalProgressFill) {
            modalProgressFill.style.width = `${progress}%`;
        }
        if (modalCurrentTime) {
            modalCurrentTime.textContent = currentTimeText;
        }
        if (modalTotalTime) {
            modalTotalTime.textContent = durationText;
        }
    }

    seekToModal(event) {
        if (!this.audio.duration) return;

        const progressContainer = event.currentTarget;
        const rect = progressContainer.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;
        const newTime = percentage * this.audio.duration;

        this.audio.currentTime = Math.max(0, Math.min(newTime, this.audio.duration));
    }

    closeMobilePlayer() {
        this.modalOverlay.style.display = 'none';
        // Mini-Player anzeigen wenn ein Song spielt
        if (this.currentSongIndex !== -1) {
            this.miniPlayer.style.display = 'flex';
        }
    }

    handleResize() {
        // Mini-Player nur im mobilen Layout anzeigen
        if (window.innerWidth > 768) {
            this.miniPlayer.style.display = 'none';
        } else if (this.currentSongIndex !== -1 && this.modalOverlay.style.display !== 'flex') {
            this.miniPlayer.style.display = 'flex';
        }
    }

    setupModalEventListeners() {
        // Play/Pause Button
        const modalPlayPause = document.getElementById('modal-play-pause-btn');
        if (modalPlayPause) {
            modalPlayPause.addEventListener('click', () => this.togglePlayPause());
        }

        // Previous/Next Buttons
        const modalPrev = document.getElementById('modal-prev-btn');
        const modalNext = document.getElementById('modal-next-btn');
        if (modalPrev) modalPrev.addEventListener('click', () => this.previousSong());
        if (modalNext) modalNext.addEventListener('click', () => this.nextSong());

        // Shuffle/Repeat Buttons
        const modalShuffle = document.getElementById('modal-shuffle-btn');
        const modalRepeat = document.getElementById('modal-repeat-btn');
        if (modalShuffle) modalShuffle.addEventListener('click', () => this.toggleShuffle());
        if (modalRepeat) modalRepeat.addEventListener('click', () => this.toggleRepeat());

        // Download Button
        const modalDownload = document.getElementById('modal-download-current-btn');
        if (modalDownload) modalDownload.addEventListener('click', () => this.downloadCurrent());

        // Progress Bar
        const modalProgressContainer = document.querySelector('.modal-progress-bar-container');
        if (modalProgressContainer) {
            modalProgressContainer.addEventListener('click', (e) => this.seekToModal(e));
        }

        // Modal Tabs
        document.querySelectorAll('.modal-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabType = e.target.dataset.tab;
                this.switchModalTab(tabType);
            });
        });

        // Button-Zustände im Modal aktualisieren
        this.updateModalButtonStates();
    }

    updateModalButtonStates() {
        // Nur aktualisieren wenn Modal geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        // Shuffle Button Zustand
        const modalShuffle = document.getElementById('modal-shuffle-btn');
        if (modalShuffle) {
            modalShuffle.classList.toggle('active', this.isShuffleMode);
        }

        // Repeat Button Zustand
        const modalRepeat = document.getElementById('modal-repeat-btn');
        if (modalRepeat) {
            // Alle Klassen entfernen
            modalRepeat.classList.remove('active', 'single-repeat');

            // Entsprechende Klassen setzen
            switch (this.repeatMode) {
                case 'playlist':
                    modalRepeat.classList.add('active');
                    this.setRepeatButtonContent(modalRepeat, false);
                    break;
                case 'single':
                    modalRepeat.classList.add('active', 'single-repeat');
                    this.setRepeatButtonContent(modalRepeat, true);
                    break;
                default:
                    this.setRepeatButtonContent(modalRepeat, false);
                    break;
            }
        }

        // Play/Pause Button Zustand
        const modalPlayPause = document.getElementById('modal-play-pause-btn');
        if (modalPlayPause) {
            const playIcon = modalPlayPause.querySelector('.play-icon');
            const pauseIcon = modalPlayPause.querySelector('.pause-icon');
            if (this.isPlaying) {
                if (playIcon) playIcon.style.display = 'none';
                if (pauseIcon) pauseIcon.style.display = 'block';
            } else {
                if (playIcon) playIcon.style.display = 'block';
                if (pauseIcon) pauseIcon.style.display = 'none';
            }
        }
    }

    handleAudioError(error) {
        console.error('Audio Fehler:', error);

        // Benutzerfreundliche Fehlermeldung
        const currentSong = this.songs[this.currentSongIndex];
        const errorMessage = `Fehler beim Laden von "${currentSong?.titel || 'Unbekanntes Lied'}".
                             Möglicherweise ist die Audiodatei nicht verfügbar.`;

        // Fehler in der UI anzeigen statt Alert
        this.showErrorMessage(errorMessage);

        // isPlaying wird durch Audio-Events automatisch gesetzt
        this.updatePlayButton();

        // Automatisch zum nächsten Lied springen nach 3 Sekunden
        setTimeout(() => {
            if (this.songs.length > 1) {
                this.nextSong();
            }
        }, 3000);
    }

    showErrorMessage(message) {
        // Temporäre Fehlermeldung in der Lyrics-Box anzeigen
        const originalContent = this.lyricsContent.innerHTML;
        this.lyricsContent.innerHTML = `
            <div style="color: #ff4444; padding: 10px; background: #fff5f5; border-radius: 4px; border-left: 4px solid #ff4444;">
                <strong>Fehler:</strong><br>
                ${message}
            </div>
        `;

        // Nach 5 Sekunden wieder original Inhalt anzeigen
        setTimeout(() => {
            this.lyricsContent.innerHTML = originalContent;
        }, 5000);
    }

    handleKeyboard(event) {
        // Nur reagieren wenn kein Input-Element fokussiert ist
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') return;

        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.togglePlayPause();
                break;
            case 'ArrowLeft':
                event.preventDefault();
                this.previousSong();
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.nextSong();
                break;
            case 'KeyS':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.toggleShuffle();
                }
                break;
            case 'KeyR':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.toggleRepeat();
                }
                break;
        }
    }

    setupLyricsTabs() {
        // Event Listener für Tab-Buttons
        document.querySelectorAll('.lyrics-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabType = e.target.dataset.tab;
                this.switchLyricsTab(tabType);
            });
        });
    }

    switchLyricsTab(tabType) {
        // Tab-Buttons aktualisieren
        document.querySelectorAll('.lyrics-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const targetTab = document.querySelector(`[data-tab="${tabType}"]`);
        if (targetTab) {
            targetTab.classList.add('active');
        }

        // Alle Content-Container verstecken
        this.lyricsContent.style.display = 'none';
        this.lyricsTranslation.style.display = 'none';
        this.lyricsInfo.style.display = 'none';

        // Gewählten Content anzeigen
        switch(tabType) {
            case 'lyrics':
                this.lyricsContent.style.display = 'block';
                break;
            case 'translation':
                this.lyricsTranslation.style.display = 'block';
                break;
            case 'info':
                this.lyricsInfo.style.display = 'block';
                break;
        }
    }

    hasTranslation(song) {
        // Prüft ob ein Song eine deutsche Übersetzung hat
        return song.translation && song.translation.trim();
    }

    hasInfo(song) {
        // Prüft ob ein Song zusätzliche Informationen hat
        return song.info && song.info.trim();
    }

    updateLyricsDisplay(song) {
        // Original Lyrics anzeigen
        if (song.lyrics && song.lyrics.trim()) {
            let formattedLyrics = this.formatLyrics(song.lyrics);
            this.lyricsContent.innerHTML = `<p>${formattedLyrics}</p>`;
        } else {
            this.lyricsContent.innerHTML = '<p class="no-lyrics">Keine Lyrics verfügbar für dieses Lied.</p>';
        }

        // Übersetzung anzeigen (falls vorhanden)
        if (this.hasTranslation(song)) {
            let formattedTranslation = this.formatLyrics(song.translation);
            this.lyricsTranslation.innerHTML = `<p>${formattedTranslation}</p>`;
        } else {
            this.lyricsTranslation.innerHTML = '<p class="no-lyrics">Keine deutsche Übersetzung verfügbar.</p>';
        }

        // Info anzeigen (falls vorhanden)
        if (this.hasInfo(song)) {
            let formattedInfo = this.formatLyrics(song.info);
            this.lyricsInfo.innerHTML = `<p>${formattedInfo}</p>`;
        } else {
            this.lyricsInfo.innerHTML = '<p class="no-lyrics">Keine zusätzlichen Informationen verfügbar.</p>';
        }

        // Tabs konfigurieren
        this.configureTabs(song);
    }

    configureTabs(song) {
        const hasTranslation = this.hasTranslation(song);
        const hasInfo = this.hasInfo(song);

        // Tab-Buttons anzeigen/verstecken
        const translationTab = document.querySelector('[data-tab="translation"]');
        const infoTab = document.querySelector('[data-tab="info"]');

        if (hasTranslation) {
            translationTab.style.display = 'block';
        } else {
            translationTab.style.display = 'none';
        }

        if (hasInfo) {
            infoTab.style.display = 'block';
        } else {
            infoTab.style.display = 'none';
        }

        // Tabs sind immer sichtbar (mindestens "Lyrics")
        this.lyricsTabs.style.display = 'flex';

        // Standard: Lyrics Tab aktiv
        this.switchLyricsTab('lyrics');
    }

    formatLyrics(lyrics) {
        // Lyrics formatieren: Doppelte Zeilenumbrüche = neue Absätze, einzelne = <br>
        let formatted = lyrics.trim();

        // Erst alle doppelten Zeilenumbrüche durch einen Platzhalter ersetzen
        formatted = formatted.replace(/\n\n+/g, '|||PARAGRAPH|||');

        // Dann einzelne Zeilenumbrüche durch <br> ersetzen
        formatted = formatted.replace(/\n/g, '<br>');

        // Platzhalter durch Absatz-Enden und -Anfänge ersetzen
        formatted = formatted.replace(/\|\|\|PARAGRAPH\|\|\|/g, '</p><p>');

        return formatted;
    }

    // Neue Modal-Funktionen
    initializeModalTabs() {
        // Standard-Tab aktivieren
        this.switchModalTab('cover');

        // Touch/Swipe-Events für Tab-Wechsel
        this.setupModalSwipeGestures();
    }

    switchModalTab(tabType) {
        // Tab-Buttons aktualisieren
        document.querySelectorAll('.modal-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const targetTab = document.querySelector(`.modal-tab[data-tab="${tabType}"]`);
        if (targetTab) {
            targetTab.classList.add('active');
        }

        // Tab-Content aktualisieren
        document.querySelectorAll('.modal-tab-content').forEach(content => {
            content.classList.remove('active', 'slide-left');
        });

        const targetContent = document.querySelector(`.modal-tab-content[data-content="${tabType}"]`);
        if (targetContent) {
            targetContent.classList.add('active');
        }

        // Translation-Tab nur anzeigen wenn vorhanden
        if (this.currentSongIndex !== -1) {
            const song = this.songs[this.currentSongIndex];
            const translationTab = document.querySelector('.modal-tab[data-tab="translation"]');
            if (translationTab) {
                translationTab.style.display = song.translation ? 'block' : 'none';
            }
        }
    }

    updateModalInfoTab(song) {
        const infoTitle = document.getElementById('modal-info-title');
        const infoArtist = document.getElementById('modal-info-artist');
        const infoAlbum = document.getElementById('modal-info-album');
        const infoGenre = document.getElementById('modal-info-genre');
        const infoLanguage = document.getElementById('modal-info-language');
        const infoAdditional = document.getElementById('modal-info-additional');

        if (infoTitle) infoTitle.textContent = song.titel;
        if (infoArtist) infoArtist.textContent = 'Jana Breitmar';
        if (infoAlbum) infoAlbum.textContent = song.album;
        if (infoGenre) infoGenre.textContent = song.genre || '-';
        if (infoLanguage) infoLanguage.textContent = song.sprache || '-';

        // Zusätzliche Info anzeigen wenn vorhanden
        if (infoAdditional && song.info) {
            infoAdditional.style.display = 'block';
            const description = infoAdditional.querySelector('.info-description');
            if (description) {
                description.textContent = song.info;
            }
        } else if (infoAdditional) {
            infoAdditional.style.display = 'none';
        }
    }

    updateModalLyricsTabs(song) {
        // Lyrics Content aktualisieren
        const lyricsContent = document.getElementById('modal-lyrics-content');
        if (lyricsContent) {
            if (song.lyrics) {
                lyricsContent.innerHTML = `<p>${this.formatLyrics(song.lyrics)}</p>`;
            } else {
                lyricsContent.innerHTML = '<p class="no-lyrics">Keine Lyrics verfügbar.</p>';
            }
        }

        // Translation Content aktualisieren
        const translationContent = document.getElementById('modal-lyrics-translation');
        if (translationContent) {
            if (song.translation) {
                translationContent.innerHTML = `<p>${this.formatLyrics(song.translation)}</p>`;
            } else {
                translationContent.innerHTML = '<p class="no-lyrics">Keine Übersetzung verfügbar.</p>';
            }
        }

        // Translation Tab sichtbar machen wenn vorhanden
        const translationTab = document.querySelector('.modal-tab[data-tab="translation"]');
        if (translationTab) {
            translationTab.style.display = song.translation ? 'block' : 'none';
        }
    }

    setupModalSwipeGestures() {
        const tabContentArea = document.querySelector('.modal-tab-content-area');
        if (!tabContentArea) return;

        let startX = 0;
        let startY = 0;
        let isSwipeActive = false;

        tabContentArea.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isSwipeActive = true;
        });

        tabContentArea.addEventListener('touchmove', (e) => {
            if (!isSwipeActive) return;

            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            const diffX = Math.abs(currentX - startX);
            const diffY = Math.abs(currentY - startY);

            // Nur horizontale Swipes verarbeiten
            if (diffX > diffY && diffX > 30) {
                e.preventDefault();
            }
        });

        tabContentArea.addEventListener('touchend', (e) => {
            if (!isSwipeActive) return;
            isSwipeActive = false;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const diffX = startX - endX;
            const diffY = Math.abs(startY - endY);

            // Mindestens 50px horizontal und weniger als 100px vertikal
            if (Math.abs(diffX) > 50 && diffY < 100) {
                const currentTab = document.querySelector('.modal-tab.active');
                if (!currentTab) return;

                const tabs = ['cover', 'lyrics', 'translation', 'info'];
                const currentTabType = currentTab.dataset.tab;
                let currentIndex = tabs.indexOf(currentTabType);

                if (diffX > 0) {
                    // Swipe left - nächster Tab
                    currentIndex++;
                } else {
                    // Swipe right - vorheriger Tab
                    currentIndex--;
                }

                // Translation-Tab überspringen wenn nicht verfügbar
                if (this.currentSongIndex !== -1) {
                    const song = this.songs[this.currentSongIndex];
                    if (tabs[currentIndex] === 'translation' && !song.translation) {
                        currentIndex += (diffX > 0) ? 1 : -1;
                    }
                }

                if (currentIndex >= 0 && currentIndex < tabs.length) {
                    this.switchModalTab(tabs[currentIndex]);
                }
            }
        });
    }
}

// Player initialisieren wenn DOM geladen ist
document.addEventListener('DOMContentLoaded', () => {
    window.musikPlayer = new MusikPlayer();
    window.musikPlayer.initialize();
});

